const mongoose = require('mongoose');

const activitySchema = new mongoose.Schema({
  // User Information
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  deviceId: {
    type: String,
    required: [true, 'Device ID is required'],
    trim: true
  },
  sessionId: {
    type: String,
    required: [true, 'Session ID is required'],
    trim: true
  },

  // Activity Details
  activityType: {
    type: String,
    enum: [
      'login', 'logout', 'screen_view', 'button_click', 'form_submit',
      'diagnosis_start', 'diagnosis_complete', 'sync_start', 'sync_complete',
      'error', 'performance', 'location_update', 'facility_visit'
    ],
    required: [true, 'Activity type is required']
  },
  
  // Screen Information
  screenName: {
    type: String,
    trim: true,
    maxlength: [100, 'Screen name cannot exceed 100 characters']
  },
  route: {
    type: String,
    trim: true,
    maxlength: [200, 'Route cannot exceed 200 characters']
  },

  // Action Details
  action: {
    name: {
      type: String,
      trim: true,
      maxlength: [100, 'Action name cannot exceed 100 characters']
    },
    target: {
      type: String,
      trim: true,
      maxlength: [100, 'Action target cannot exceed 100 characters']
    },
    value: mongoose.Schema.Types.Mixed
  },

  // Location Information
  location: {
    latitude: Number,
    longitude: Number,
    accuracy: Number,
    address: {
      type: String,
      trim: true,
      maxlength: [200, 'Address cannot exceed 200 characters']
    },
    facility: {
      type: String,
      trim: true,
      maxlength: [100, 'Facility name cannot exceed 100 characters']
    },
    facilityType: {
      type: String,
      enum: ['hospital', 'clinic', 'pharmacy', 'laboratory', 'other'],
      default: 'other'
    }
  },

  // Performance Metrics
  performance: {
    duration: Number, // in milliseconds
    loadTime: Number, // in milliseconds
    memoryUsage: Number, // in MB
    networkLatency: Number, // in milliseconds
    batteryLevel: Number // percentage
  },

  // Error Information
  error: {
    code: {
      type: String,
      trim: true,
      maxlength: [50, 'Error code cannot exceed 50 characters']
    },
    message: {
      type: String,
      trim: true,
      maxlength: [500, 'Error message cannot exceed 500 characters']
    },
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'medium'
    },
    stack: {
      type: String,
      trim: true
    }
  },

  // Metadata
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },

  // Device Information
  deviceInfo: {
    platform: {
      type: String,
      enum: ['ios', 'android', 'web'],
      required: [true, 'Platform is required']
    },
    version: {
      type: String,
      trim: true,
      maxlength: [20, 'Version cannot exceed 20 characters']
    },
    model: {
      type: String,
      trim: true,
      maxlength: [50, 'Model cannot exceed 50 characters']
    },
    osVersion: {
      type: String,
      trim: true,
      maxlength: [20, 'OS version cannot exceed 20 characters']
    }
  },

  // Network Information
  networkInfo: {
    type: {
      type: String,
      enum: ['wifi', 'cellular', 'ethernet', 'unknown'],
      default: 'unknown'
    },
    isConnected: {
      type: Boolean,
      default: true
    },
    strength: Number // signal strength percentage
  },

  // Sync Status
  syncStatus: {
    type: String,
    enum: ['pending', 'synced', 'failed'],
    default: 'pending'
  },
  syncedAt: {
    type: Date
  },
  syncAttempts: {
    type: Number,
    default: 0
  },

  // Timestamps
  timestamp: {
    type: Date,
    default: Date.now,
    required: [true, 'Timestamp is required']
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes for performance
activitySchema.index({ userId: 1, timestamp: -1 });
activitySchema.index({ deviceId: 1, timestamp: -1 });
activitySchema.index({ sessionId: 1, timestamp: -1 });
activitySchema.index({ activityType: 1, timestamp: -1 });
activitySchema.index({ syncStatus: 1 });
activitySchema.index({ 'location.facility': 1 });
activitySchema.index({ timestamp: -1 });

// Compound indexes
activitySchema.index({ userId: 1, activityType: 1, timestamp: -1 });
activitySchema.index({ deviceId: 1, syncStatus: 1 });

// Static method to get activity statistics
activitySchema.statics.getActivityStats = function(userId, startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        userId: mongoose.Types.ObjectId(userId),
        timestamp: {
          $gte: startDate,
          $lte: endDate
        }
      }
    },
    {
      $group: {
        _id: '$activityType',
        count: { $sum: 1 },
        lastActivity: { $max: '$timestamp' }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);
};

// Static method to get error statistics
activitySchema.statics.getErrorStats = function(startDate, endDate) {
  return this.aggregate([
    {
      $match: {
        activityType: 'error',
        timestamp: {
          $gte: startDate,
          $lte: endDate
        }
      }
    },
    {
      $group: {
        _id: {
          code: '$error.code',
          severity: '$error.severity'
        },
        count: { $sum: 1 },
        lastOccurrence: { $max: '$timestamp' },
        users: { $addToSet: '$userId' }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);
};

module.exports = mongoose.model('Activity', activitySchema);
