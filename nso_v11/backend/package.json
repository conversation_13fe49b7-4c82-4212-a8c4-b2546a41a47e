{"name": "nso-backend", "version": "1.0.0", "description": "NSO Mobile App Backend - Data Sync and User Activity Tracking", "main": "server.js", "type": "commonjs", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "lint": "eslint .", "seed": "node scripts/seedData.js"}, "keywords": ["nso", "healthcare", "mobile-backend", "sync", "medical"], "author": "NSO Development Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "pg": "^8.16.3", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}