# Server Configuration
PORT=3000
NODE_ENV=production

# Database Configuration
MONGODB_URL=mongodb+srv://nso:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0

# JWT Configuration
JWT_SECRET=nso-app-secret-key-2024-change-in-production
JWT_EXPIRES_IN=7d

# CORS Configuration (comma-separated origins)
CORS_ORIGINS='*'

# Logging
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10485760

# Security
BCRYPT_ROUNDS=12

# Sync Configuration
SYNC_BATCH_SIZE=50
SYNC_TIMEOUT=30000
