const mongoose = require('mongoose');
require('dotenv').config();

async function testMongoConnection() {
  try {
    console.log('Testing MongoDB connection...');
    console.log('Connection string:', process.env.MONGODB_URL.replace(/:[^:@]*@/, ':****@'));
    
    await mongoose.connect(process.env.MONGODB_URL, {
      serverSelectionTimeoutMS: 10000,
      connectTimeoutMS: 10000,
    });
    
    console.log('✅ Connected to MongoDB successfully!');
    
    // Test a simple operation
    const adminDb = mongoose.connection.db.admin();
    const result = await adminDb.ping();
    console.log('✅ Database ping successful:', result);
    
    // Get database info
    const dbStats = await mongoose.connection.db.stats();
    console.log('✅ Database stats:', {
      dbName: dbStats.db,
      collections: dbStats.collections,
      dataSize: dbStats.dataSize
    });
    
    await mongoose.connection.close();
    console.log('✅ Connection test completed successfully');
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('Error code:', error.code);
    console.error('Error name:', error.name);
    
    if (error.reason) {
      console.error('Error reason:', error.reason);
    }
    
    process.exit(1);
  }
}

testMongoConnection();
